## Project Brief: Shopify Stock and Pricing Microservice

### Objective
Deploy a microservice to fetch real-time stock levels and pricing information from Shopify using their GraphQL API, based on provided SKUs.

### Requirements
- **Platform**: Deploy on a cloud platform (DigitalOcean recommended, or Render)
- **API Integration**: Utilise Shopify's GraphQL API for data retrieval
- **Input**: Accept SKU(s) as input parameters
- **Output**: Return stock levels and pricing data in structured format (JSON)
- **Performance**: Ensure low latency responses for real-time queries

### Technical Specifications
- Implement proper error handling and logging
- Include authentication for Shopify API access
- Consider rate limiting and caching strategies
- Provide RESTful endpoints for easy integration
- Include health check endpoints for monitoring

### Deliverables
- Deployed microservice with public API endpoints
- Documentation for API usage
- Basic monitoring and logging setup